import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import requests
import json
import threading
import time
import socket
import os
import sys
from flask import Flask, request
from flask_socketio import SocketIO
import eventlet
from models import Database
from socket_handler import SocketHandler

class TypingContestManager:
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 太空打字挑战赛 - 管理控制台")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)

        # 设置窗口图标和样式
        self.setup_window_style()

        # 服务器状态
        self.server_running = False
        self.socketio = None
        self.app = None

        # 获取本机IP
        self.server_ip = self.get_local_ip()
        self.server_port = 5000
        self.server_url = f"http://{self.server_ip}:{self.server_port}"
        self.api_url = f"{self.server_url}/api"

        # 当前比赛ID
        self.current_contest_id = None
        self.contest_status = "waiting"

        # 创建数据库对象
        self.db = Database()

        # 初始化设置
        self.leaderboard_limit = self.db.get_leaderboard_limit()

        # 创建UI
        self.create_ui()

        # 启动数据更新线程
        self.update_thread = threading.Thread(target=self.update_data_periodically, daemon=True)
        self.update_thread.start()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_window_style(self):
        """设置窗口样式"""
        try:
            # 设置窗口背景色
            self.root.configure(bg='#1a1a2e')

            # 配置ttk样式
            style = ttk.Style()
            style.theme_use('clam')

            # 配置各种组件的样式
            style.configure('Space.TFrame', background='#1a1a2e')
            style.configure('Space.TLabelFrame', background='#1a1a2e', foreground='#ffffff',
                          borderwidth=2, relief='solid')
            style.configure('Space.TLabelFrame.Label', background='#1a1a2e', foreground='#4dabf7',
                          font=('Arial', 10, 'bold'))
            style.configure('Space.TLabel', background='#1a1a2e', foreground='#ffffff')
            style.configure('Space.TButton', background='#536dfe', foreground='#ffffff',
                          borderwidth=1, focuscolor='none')
            style.map('Space.TButton',
                     background=[('active', '#3f51b5'), ('pressed', '#303f9f')])
            style.configure('Space.TEntry', fieldbackground='#252d5a', foreground='#ffffff',
                          borderwidth=1, insertcolor='#ffffff')
            style.configure('Space.TCheckbutton', background='#1a1a2e', foreground='#ffffff',
                          focuscolor='none')
            style.configure('Space.TSpinbox', fieldbackground='#252d5a', foreground='#ffffff',
                          borderwidth=1, insertcolor='#ffffff')

            # 配置Treeview样式
            style.configure('Space.Treeview', background='#252d5a', foreground='#ffffff',
                          fieldbackground='#252d5a', borderwidth=1)
            style.configure('Space.Treeview.Heading', background='#536dfe', foreground='#ffffff',
                          borderwidth=1)
            style.map('Space.Treeview', background=[('selected', '#3f51b5')])

        except Exception as e:
            print(f"设置窗口样式失败: {e}")

    def get_local_ip(self):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"

    def create_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="15", style='Space.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题
        title_frame = ttk.Frame(main_frame, style='Space.TFrame')
        title_frame.pack(fill=tk.X, pady=(0, 15))

        title_label = ttk.Label(title_frame, text="🚀 太空打字挑战赛 - 管理控制台",
                               font=('Arial', 16, 'bold'), style='Space.TLabel')
        title_label.pack()

        # 创建左右分栏
        content_frame = ttk.Frame(main_frame, style='Space.TFrame')
        content_frame.pack(fill=tk.BOTH, expand=True)

        left_frame = ttk.Frame(content_frame, style='Space.TFrame')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        right_frame = ttk.Frame(content_frame, style='Space.TFrame')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        # 左侧 - 服务器信息和比赛控制
        server_frame = ttk.LabelFrame(left_frame, text="🖥️ 服务器状态", padding="15", style='Space.TLabelFrame')
        server_frame.pack(fill=tk.X, pady=(0, 10))

        # 服务器控制按钮
        self.server_btn = ttk.Button(server_frame, text="🚀 启动服务器", command=self.toggle_server, style='Space.TButton')
        self.server_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 服务器状态标签
        self.server_status = ttk.Label(server_frame, text="状态: 未运行", style='Space.TLabel')
        self.server_status.pack(side=tk.LEFT, padx=(0, 10))

        # IP地址显示和复制框架
        ip_frame = ttk.Frame(server_frame, style='Space.TFrame')
        ip_frame.pack(side=tk.LEFT, padx=(0, 10))

        # 服务器地址标签
        self.server_address = ttk.Label(ip_frame, text="地址: 未启动", style='Space.TLabel')
        self.server_address.pack(side=tk.LEFT)

        # IP地址文本框（用于复制）
        self.ip_entry = ttk.Entry(ip_frame, width=30, style='Space.TEntry')
        self.ip_entry.pack(side=tk.LEFT, padx=(10, 0))

        # 比赛文本设置
        text_frame = ttk.LabelFrame(left_frame, text="📝 比赛文本设置", padding="15", style='Space.TLabelFrame')
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.text_editor = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD, height=10,
                                                   bg='#252d5a', fg='#ffffff', insertbackground='#ffffff',
                                                   selectbackground='#536dfe', selectforeground='#ffffff')
        self.text_editor.pack(fill=tk.BOTH, expand=True)

        # 示例文本
        sample_text = "hello world welcome to typing game good morning nice to meet you today is a beautiful day let us practice typing together"
        self.text_editor.insert(tk.END, sample_text)

        # 比赛控制按钮
        control_frame = ttk.LabelFrame(left_frame, text="🎮 比赛控制", padding="15", style='Space.TLabelFrame')
        control_frame.pack(fill=tk.X, pady=(0, 10))

        btn_frame = ttk.Frame(control_frame, style='Space.TFrame')
        btn_frame.pack(fill=tk.X)

        self.create_btn = ttk.Button(btn_frame, text="🆕 创建比赛", command=self.create_contest, style='Space.TButton')
        self.create_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.start_btn = ttk.Button(btn_frame, text="▶️ 开始比赛", command=self.start_contest, style='Space.TButton')
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.pause_btn = ttk.Button(btn_frame, text="⏸️ 暂停比赛", command=self.pause_contest, style='Space.TButton')
        self.pause_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.end_btn = ttk.Button(btn_frame, text="⏹️ 结束比赛", command=self.end_contest, style='Space.TButton')
        self.end_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 添加排行榜显示控制
        self.leaderboard_visible = tk.BooleanVar(value=True)
        leaderboard_check = ttk.Checkbutton(
            control_frame,
            text="📊 显示客户端排行榜",
            variable=self.leaderboard_visible,
            command=self.toggle_leaderboard_visibility,
            style='Space.TCheckbutton'
        )
        leaderboard_check.pack(anchor=tk.W, pady=(10, 0))

        # 状态显示
        self.status_var = tk.StringVar(value="⏳ 等待创建比赛")
        status_label = ttk.Label(control_frame, textvariable=self.status_var,
                               font=("Arial", 11, "bold"), style='Space.TLabel')
        status_label.pack(pady=(10, 0))

        # 右侧 - 用户管理和排行榜
        # 在线用户列表
        users_frame = ttk.LabelFrame(right_frame, text="👥 在线用户", padding="15", style='Space.TLabelFrame')
        users_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 用户计数
        self.user_count_var = tk.StringVar(value="在线用户: 0")
        ttk.Label(users_frame, textvariable=self.user_count_var,
                 font=("Arial", 11, "bold"), style='Space.TLabel').pack(anchor=tk.W, pady=(0, 10))

        # 创建表格容器
        tree_frame = ttk.Frame(users_frame, style='Space.TFrame')
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表格
        columns = ("昵称", "IP地址", "正确数", "错误数", "准确率", "得分", "操作")
        self.users_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", style='Space.Treeview')

        # 设置列标题
        for col in columns:
            self.users_tree.heading(col, text=col)
            width = 120 if col != "操作" else 80
            self.users_tree.column(col, width=width, anchor='center')

        # 添加滚动条
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)

        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定踢出用户事件
        self.users_tree.bind("<Double-1>", self.kick_user_dialog)

        # 排行榜
        leaderboard_frame = ttk.LabelFrame(right_frame, text="🏆 排行榜", padding="15", style='Space.TLabelFrame')
        leaderboard_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建排行榜表格容器
        lb_tree_frame = ttk.Frame(leaderboard_frame, style='Space.TFrame')
        lb_tree_frame.pack(fill=tk.BOTH, expand=True)

        # 创建排行榜表格
        lb_columns = ("排名", "昵称", "正确数", "错误数", "准确率", "得分")
        self.leaderboard_tree = ttk.Treeview(lb_tree_frame, columns=lb_columns, show="headings", style='Space.Treeview')

        # 设置列标题
        for col in lb_columns:
            self.leaderboard_tree.heading(col, text=col)
            width = 100
            self.leaderboard_tree.column(col, width=width, anchor='center')

        # 添加滚动条
        lb_scrollbar = ttk.Scrollbar(lb_tree_frame, orient=tk.VERTICAL, command=self.leaderboard_tree.yview)
        self.leaderboard_tree.configure(yscrollcommand=lb_scrollbar.set)

        self.leaderboard_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        lb_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 在排行榜管理框架中添加排行榜显示数量设置
        leaderboard_config_frame = ttk.LabelFrame(right_frame, text="⚙️ 排行榜设置", padding="15", style='Space.TLabelFrame')
        leaderboard_config_frame.pack(fill=tk.X)

        # 排行榜可见性设置
        leaderboard_visibility_frame = ttk.Frame(leaderboard_config_frame, style='Space.TFrame')
        leaderboard_visibility_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(leaderboard_visibility_frame, text="排行榜可见性:", style='Space.TLabel').pack(side=tk.LEFT, padx=(0, 10))

        self.leaderboard_visible_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(leaderboard_visibility_frame, text="显示排行榜",
                       variable=self.leaderboard_visible_var,
                       command=self.toggle_leaderboard_visibility,
                       style='Space.TCheckbutton').pack(side=tk.LEFT)

        # 排行榜显示数量设置
        leaderboard_limit_frame = ttk.Frame(leaderboard_config_frame, style='Space.TFrame')
        leaderboard_limit_frame.pack(fill=tk.X)

        ttk.Label(leaderboard_limit_frame, text="显示人数:", style='Space.TLabel').pack(side=tk.LEFT, padx=(0, 10))

        # 创建一个整数变量，初始值为当前数据库设置或默认值10
        self.leaderboard_limit_var = tk.IntVar(value=self.leaderboard_limit)

        # 使用Spinbox让用户选择1-50之间的整数
        limit_spinbox = ttk.Spinbox(
            leaderboard_limit_frame,
            from_=1,
            to=50,
            width=8,
            textvariable=self.leaderboard_limit_var,
            command=lambda: self.update_leaderboard_limit(None),
            style='Space.TSpinbox'
        )
        limit_spinbox.pack(side=tk.LEFT, padx=(0, 10))

        # 绑定输入验证，确保只能输入有效的整数
        limit_spinbox.bind('<FocusOut>', self.update_leaderboard_limit)
        limit_spinbox.bind('<Return>', self.update_leaderboard_limit)

        ttk.Button(
            leaderboard_limit_frame,
            text="✅ 应用",
            command=lambda: self.update_leaderboard_limit(None),
            style='Space.TButton'
        ).pack(side=tk.LEFT)

    def create_contest(self):
        text_content = self.text_editor.get("1.0", tk.END).strip()
        if not text_content:
            messagebox.showerror("错误", "请输入比赛文本")
            return

        try:
            # 确保文本内容使用UTF-8编码
            response = requests.post(
                f"{self.api_url}/admin/contest/create",
                json={"text_content": text_content},
                timeout=5,
                headers={"Content-Type": "application/json; charset=utf-8"}
            )
            data = response.json()
            self.current_contest_id = data.get('contest_id')
            self.contest_status = "waiting"
            self.status_var.set("🎯 比赛已创建，等待开始")
            messagebox.showinfo("🎉 成功", "比赛已创建，等待开始！")
        except Exception as e:
            messagebox.showerror("错误", f"创建比赛失败: {str(e)}")

    def start_contest(self):
        if not self.current_contest_id:
            messagebox.showerror("错误", "请先创建比赛")
            return

        try:
            response = requests.post(
                f"{self.api_url}/admin/contest/start",
                json={"contest_id": self.current_contest_id},
                timeout=5
            )
            self.contest_status = "running"
            self.status_var.set("🚀 比赛进行中")
            messagebox.showinfo("🎉 成功", "比赛已开始！")
        except Exception as e:
            messagebox.showerror("错误", f"开始比赛失败: {str(e)}")

    def pause_contest(self):
        if not self.current_contest_id or self.contest_status != "running":
            messagebox.showerror("错误", "没有正在进行的比赛")
            return

        try:
            response = requests.post(
                f"{self.api_url}/admin/contest/pause",
                json={"contest_id": self.current_contest_id},
                timeout=5
            )
            self.contest_status = "paused"
            self.status_var.set("⏸️ 比赛已暂停")
            messagebox.showinfo("🎉 成功", "比赛已暂停！")
        except Exception as e:
            messagebox.showerror("错误", f"暂停比赛失败: {str(e)}")

    def end_contest(self):
        if not self.current_contest_id:
            messagebox.showerror("错误", "没有活动的比赛")
            return

        try:
            response = requests.post(
                f"{self.api_url}/admin/contest/end",
                json={"contest_id": self.current_contest_id},
                timeout=5
            )
            self.contest_status = "ended"
            self.status_var.set("🏁 比赛已结束")
            messagebox.showinfo("🎉 成功", "比赛已结束！")
        except Exception as e:
            messagebox.showerror("错误", f"结束比赛失败: {str(e)}")

    def kick_user_dialog(self, event):
        item = self.users_tree.selection()[0]
        user_data = self.users_tree.item(item, "values")
        nickname = user_data[0]
        session_id = self.users_tree.item(item, "tags")[0]

        if messagebox.askyesno("踢出用户", f"确定要踢出用户 {nickname} 吗?"):
            self.kick_user(session_id)

    def kick_user(self, session_id):
        try:
            response = requests.post(
                f"{self.api_url}/admin/user/kick",
                json={"session_id": session_id},
                timeout=5
            )
            data = response.json()
            if data.get('status') == 'success':
                messagebox.showinfo("成功", "用户已被踢出")
            else:
                messagebox.showerror("错误", "踢出用户失败")
        except Exception as e:
            messagebox.showerror("错误", f"踢出用户失败: {str(e)}")

    def update_data_periodically(self):
        """定期更新用户列表和排行榜"""
        while True:
            try:
                self.update_contest_status()
                self.update_users_list()
                self.update_leaderboard()
            except Exception as e:
                print(f"更新数据失败: {str(e)}")

            time.sleep(2)  # 每2秒更新一次

    def update_contest_status(self):
        """更新比赛状态"""
        try:
            response = requests.get(f"{self.api_url}/contest/status", timeout=5)
            data = response.json()

            if data:
                self.current_contest_id = data.get('id')
                self.contest_status = data.get('status')

                status_text = {
                    'waiting': "⏳ 等待开始",
                    'running': "🚀 比赛进行中",
                    'paused': "⏸️ 比赛已暂停",
                    'ended': "🏁 比赛已结束"
                }.get(self.contest_status, "❓ 未知状态")

                self.status_var.set(f"{status_text}")
            else:
                self.status_var.set("💤 没有活动的比赛")
        except Exception as e:
            print(f"更新比赛状态失败: {str(e)}")

    def update_users_list(self):
        """更新用户列表"""
        try:
            response = requests.get(f"{self.api_url}/users/online", timeout=5)
            users = response.json()

            # 更新用户计数
            self.user_count_var.set(f"在线用户: {len(users)}")

            # 清空表格
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            # 添加用户数据
            for user in users:
                values = (
                    user.get('nickname', ''),
                    user.get('ip', ''),
                    user.get('correct_count', 0),
                    user.get('error_count', 0),
                    f"{user.get('accuracy', 0.0):.1f}%",
                    user.get('score', 0),
                    "踢出"
                )
                self.users_tree.insert('', 'end', values=values, tags=(user.get('session_id'),))
        except Exception as e:
            print(f"更新用户列表失败: {str(e)}")

    def update_leaderboard(self):
        """更新排行榜"""
        if not self.current_contest_id:
            return

        try:
            # 获取在线用户数据
            response = requests.get(f"{self.api_url}/users/online", timeout=5)
            users = response.json()

            if not users:
                return

            # 清空表格
            for item in self.leaderboard_tree.get_children():
                self.leaderboard_tree.delete(item)

            # 按得分排序
            users.sort(key=lambda x: x.get('score', 0), reverse=True)

            # 添加排行榜数据
            for i, user in enumerate(users[:10], 1):
                values = (
                    i,
                    user.get('nickname', ''),
                    user.get('correct_count', 0),
                    user.get('error_count', 0),
                    f"{user.get('accuracy', 0.0):.1f}%",
                    user.get('score', 0)
                )
                self.leaderboard_tree.insert('', 'end', values=values)
        except Exception as e:
            print(f"更新排行榜失败: {str(e)}")

    def toggle_server(self):
        """切换服务器状态"""
        if not self.server_running:
            self.start_server()
        else:
            self.stop_server()

    def start_server(self):
        """启动服务器"""
        try:
            if self.server_running:
                return

            # 初始化Flask应用
            self.app = Flask(__name__,
                           static_folder='../client' if not getattr(sys, 'frozen', False) else 'client',
                           static_url_path='')
            self.app.config['SECRET_KEY'] = 'typing_contest_secret_key'

            # 初始化SocketIO
            self.socketio = SocketIO(self.app, cors_allowed_origins="*", async_mode='eventlet')

            # 初始化Socket处理器
            self.socket_handler = SocketHandler(self.socketio, self.db)

            # 设置路由
            self.setup_routes()

            # 在新线程中启动服务器
            self.server_thread = threading.Thread(target=self.run_server, daemon=True)
            self.server_thread.start()

            # 等待服务器启动
            time.sleep(2)
            try:
                response = requests.get(f"http://{self.server_ip}:{self.server_port}/api/contest/status")
                if response.status_code == 200:
                    self.server_running = True
                    self.server_btn.configure(text="🛑 停止服务器")
                    self.server_status.configure(text="状态: ✅ 运行中")
                    server_url = f"http://{self.server_ip}:{self.server_port}"
                    self.server_address.configure(text="地址:")
                    self.ip_entry.delete(0, tk.END)
                    self.ip_entry.insert(0, server_url)
                    messagebox.showinfo("🎉 成功", "服务器已启动！")
                    return
            except:
                pass

            messagebox.showerror("错误", "服务器启动失败")

        except Exception as e:
            messagebox.showerror("错误", f"启动服务器失败: {str(e)}")

    def setup_routes(self):
        """设置Flask路由"""
        @self.app.route('/')
        def index():
            return self.app.send_static_file('index.html')

        @self.app.route('/api/contest/status')
        def get_contest_status():
            contest = self.db.get_active_contest()
            return json.dumps(contest if contest else {})

        @self.app.route('/api/users/online')
        def get_online_users():
            users = self.db.get_all_online_users()
            return json.dumps(users)

        # 添加管理员API路由
        @self.app.route('/api/admin/contest/create', methods=['POST'])
        def create_contest():
            data = request.get_json(force=True)  # 强制解析JSON
            text_content = data.get('text_content', '')

            if not text_content:
                return json.dumps({'error': 'Text content is required'}, ensure_ascii=False), 400, {'Content-Type': 'application/json; charset=utf-8'}

            # 确保文本内容是UTF-8编码
            if isinstance(text_content, str):
                # 已经是字符串，不需要额外处理
                pass
            elif isinstance(text_content, bytes):
                # 如果是字节，解码为字符串
                text_content = text_content.decode('utf-8')

            contest_id = self.db.create_contest(text_content)
            self.current_contest_id = contest_id
            return json.dumps({'contest_id': contest_id, 'status': 'waiting'}, ensure_ascii=False), 200, {'Content-Type': 'application/json; charset=utf-8'}

        @self.app.route('/api/admin/contest/start', methods=['POST'])
        def start_contest():
            data = request.get_json()
            contest_id = data.get('contest_id')

            if not contest_id:
                contest = self.db.get_active_contest()
                if contest:
                    contest_id = contest['id']
                else:
                    return json.dumps({'error': 'No active contest found'}), 404

            self.db.start_contest(contest_id)
            self.socket_handler.broadcast_contest_status('running', contest_id)
            return json.dumps({'status': 'success'})

        @self.app.route('/api/admin/contest/pause', methods=['POST'])
        def pause_contest():
            data = request.get_json()
            contest_id = data.get('contest_id')

            if not contest_id:
                contest = self.db.get_active_contest()
                if contest:
                    contest_id = contest['id']
                else:
                    return json.dumps({'error': 'No active contest found'}), 404

            self.db.pause_contest(contest_id)
            self.socket_handler.broadcast_contest_status('paused', contest_id)
            return json.dumps({'status': 'success'})

        @self.app.route('/api/admin/contest/end', methods=['POST'])
        def end_contest():
            data = request.get_json()
            contest_id = data.get('contest_id')

            if not contest_id:
                contest = self.db.get_active_contest()
                if contest:
                    contest_id = contest['id']
                else:
                    return json.dumps({'error': 'No active contest found'}), 404

            self.db.end_contest(contest_id)
            self.socket_handler.broadcast_contest_status('ended', contest_id)
            return json.dumps({'status': 'success'})

        @self.app.route('/api/admin/user/kick', methods=['POST'])
        def kick_user():
            data = request.get_json()
            session_id = data.get('session_id')

            if not session_id:
                return json.dumps({'error': 'Session ID is required'}), 400

            success = self.socket_handler.kick_user(session_id)
            return json.dumps({'status': 'success' if success else 'failed'})

        # 添加排行榜显示控制API
        @self.app.route('/api/admin/leaderboard/visibility', methods=['POST'])
        def toggle_leaderboard_visibility():
            data = request.get_json()
            visible = data.get('visible', True)

            # 存储排行榜可见性设置
            self.db.set_setting('leaderboard_visible', visible)

            # 广播排行榜显示状态
            self.socket_handler.broadcast_leaderboard_visibility(visible)

            return json.dumps({'status': 'success'})

        # 获取排行榜显示状态API
        @self.app.route('/api/leaderboard/visibility')
        def get_leaderboard_visibility():
            visible = self.db.get_setting('leaderboard_visible', default=True)
            return json.dumps({'visible': visible})

    def run_server(self):
        """在新线程中运行服务器"""
        eventlet.monkey_patch()
        self.socketio.run(self.app, host=self.server_ip, port=self.server_port)

    def stop_server(self):
        """停止服务器"""
        if self.server_running and self.socketio:
            try:
                self.socketio.stop()
                self.server_running = False
                self.server_btn.configure(text="🚀 启动服务器")
                self.server_status.configure(text="状态: ❌ 未运行")
                self.server_address.configure(text="地址: 未启动")
            except:
                pass

    def on_closing(self):
        """窗口关闭时的处理"""
        if self.server_running:
            if messagebox.askokcancel("确认", "服务器正在运行，确定要退出吗？"):
                self.stop_server()
                self.root.destroy()
        else:
            self.root.destroy()

    def toggle_leaderboard_visibility(self):
        """切换排行榜显示状态"""
        if not self.server_running:
            messagebox.showerror("错误", "服务器未运行")
            return

        is_visible = self.leaderboard_visible.get()
        try:
            response = requests.post(
                f"{self.api_url}/admin/leaderboard/visibility",
                json={"visible": is_visible},
                timeout=5
            )
            data = response.json()
            if data.get('status') == 'success':
                status_text = "显示" if is_visible else "隐藏"
                messagebox.showinfo("成功", f"排行榜已{status_text}")
            else:
                messagebox.showerror("错误", "设置排行榜可见性失败")
        except Exception as e:
            messagebox.showerror("错误", f"设置排行榜可见性失败: {str(e)}")

    def update_leaderboard_limit(self, event=None):
        """更新排行榜显示数量设置"""
        try:
            # 获取输入的值并验证
            limit = self.leaderboard_limit_var.get()

            # 确保值在合理范围内
            if limit < 1:
                limit = 1
                self.leaderboard_limit_var.set(1)
            elif limit > 50:
                limit = 50
                self.leaderboard_limit_var.set(50)

            # 保存到数据库
            if self.db.set_leaderboard_limit(limit):
                # 如果有活跃的比赛，广播更新的排行榜
                if self.current_contest_id:
                    self.socket_handler.broadcast_leaderboard()
                messagebox.showinfo("成功", f"排行榜显示数量已设置为: {limit}")
            else:
                messagebox.showerror("错误", "设置排行榜显示数量失败")
        except Exception as e:
            messagebox.showerror("错误", f"设置排行榜显示数量失败: {str(e)}")

# 启动管理界面
if __name__ == "__main__":
    def validate_password():
        password_window = tk.Toplevel()
        password_window.title("验证")
        password_window.geometry("300x150")
        password_window.resizable(False, False)

        # 设置窗口在屏幕中央
        window_width = 300
        window_height = 150
        screen_width = password_window.winfo_screenwidth()
        screen_height = password_window.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        password_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 阻止调整大小
        password_window.minsize(window_width, window_height)
        password_window.maxsize(window_width, window_height)

        # 密码输入框
        frame = ttk.Frame(password_window, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(frame, text="请输入:").pack(pady=(0, 10))

        password_var = tk.StringVar()
        password_entry = ttk.Entry(frame, textvariable=password_var, show="*", width=20)
        password_entry.pack(pady=(0, 15))
        password_entry.focus()

        result = [False]  # 使用列表存储结果，以便在函数内部修改

        def check_password():
            if password_var.get() == "12340":
                result[0] = True
                password_window.destroy()
            else:
                ttk.Label(frame, text="密码错误！", foreground="red").pack()
                password_entry.delete(0, tk.END)
                password_entry.focus()

        def on_enter(event):
            check_password()

        password_entry.bind("<Return>", on_enter)

        ttk.Button(frame, text="确认", command=check_password).pack()

        # 保持窗口置顶
        password_window.attributes("-topmost", True)

        # 捕获窗口关闭事件
        password_window.protocol("WM_DELETE_WINDOW", lambda: sys.exit(0))

        # 等待窗口关闭
        root.wait_window(password_window)
        return result[0]

    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    if validate_password():
        root.deiconify()  # 显示主窗口
        app = TypingContestManager(root)
        root.mainloop()
    else:
        root.destroy()
        sys.exit(0)